const invoiceService = require('../services/invoiceService');
const { Invoices, Workspace, User } = require('../models');
const { Op } = require('sequelize');

async function testCronJob() {
    console.log('=== KIỂM TRA CRON JOB CẬP NHẬT PACKAGE HẾT HẠN ===\n');

    try {
        // 1. Kiểm tra kết nối database
        console.log('1. Kiểm tra kết nối database...');
        const testConnection = await Invoices.findOne({ limit: 1 });
        console.log('✓ Kết nối database thành công\n');

        // 2. <PERSON>ểm tra hóa đơn hết hạn hiện tại
        console.log('2. Kiểm tra hóa đơn hết hạn hiện tại...');
        const now = new Date();
        const expiredInvoices = await Invoices.findAll({
            where: {
                status: 'paid',
                packageEndDate: {
                    [Op.lt]: now
                }
            },
            include: [
                { model: User, attributes: ['id', 'email', 'name'] },
                { model: Workspace, attributes: ['id', 'name', 'packageId'] }
            ]
        });

        console.log(`<PERSON>ì<PERSON> thấy ${expiredInvoices.length} hóa đơn hết hạn cần xử lý:`);
        expiredInvoices.forEach(invoice => {
            console.log(`- Hóa đơn ID: ${invoice.id}, Workspace: ${invoice.Workspace?.name}, Hết hạn: ${invoice.packageEndDate}`);
        });
        console.log('');

        // 3. Kiểm tra hóa đơn sắp hết hạn (7 ngày tới)
        console.log('3. Kiểm tra hóa đơn sắp hết hạn (7 ngày tới)...');
        const soonExpiring = await invoiceService.checkPackageExpiration();
        console.log(`Tìm thấy ${soonExpiring.length} hóa đơn sắp hết hạn trong 7 ngày tới:`);
        soonExpiring.forEach(invoice => {
            console.log(`- Hóa đơn ID: ${invoice.id}, Workspace: ${invoice.Workspace?.name}, Hết hạn: ${invoice.packageEndDate}`);
        });
        console.log('');

        // 4. Test chạy thử cron job (DRY RUN)
        console.log('4. Test chạy thử cron job (DRY RUN)...');
        if (expiredInvoices.length > 0) {
            console.log('⚠️  CẢNH BÁO: Có hóa đơn hết hạn cần xử lý!');
            console.log('Để chạy thực tế, sử dụng: npm run cron:update-package');
            console.log('Hoặc gọi: await invoiceService.processExpiredInvoices()');
        } else {
            console.log('✓ Không có hóa đơn hết hạn nào cần xử lý');
        }
        console.log('');

        // 5. Kiểm tra workspace có package khác Free
        console.log('5. Kiểm tra workspace có package khác Free...');
        const paidWorkspaces = await Workspace.findAll({
            where: {
                packageId: {
                    [Op.ne]: 1 // Khác package Free
                }
            },
            attributes: ['id', 'name', 'packageId'],
            include: [
                {
                    model: Invoices,
                    as: 'invoices',
                    where: {
                        status: 'paid'
                    },
                    attributes: ['id', 'packageEndDate', 'status'],
                    required: false
                }
            ]
        });

        console.log(`Tìm thấy ${paidWorkspaces.length} workspace có package trả phí:`);
        paidWorkspaces.forEach(workspace => {
            const activeInvoices = workspace.invoices?.filter(inv => new Date(inv.packageEndDate) >= now) || [];
            const expiredInvoices = workspace.invoices?.filter(inv => new Date(inv.packageEndDate) < now) || [];
            
            console.log(`- Workspace: ${workspace.name} (Package ${workspace.packageId})`);
            console.log(`  + Hóa đơn còn hạn: ${activeInvoices.length}`);
            console.log(`  + Hóa đơn hết hạn: ${expiredInvoices.length}`);
        });
        console.log('');

        // 6. Thống kê tổng quan
        console.log('6. Thống kê tổng quan...');
        const totalInvoices = await Invoices.count();
        const paidInvoices = await Invoices.count({ where: { status: 'paid' } });
        const expiredInvoicesCount = await Invoices.count({ where: { status: 'expired' } });
        const pendingInvoices = await Invoices.count({ where: { status: 'pending' } });

        console.log(`- Tổng số hóa đơn: ${totalInvoices}`);
        console.log(`- Hóa đơn đã thanh toán: ${paidInvoices}`);
        console.log(`- Hóa đơn hết hạn: ${expiredInvoicesCount}`);
        console.log(`- Hóa đơn chờ thanh toán: ${pendingInvoices}`);

        console.log('\n=== KIỂM TRA HOÀN THÀNH ===');
        console.log('✓ Tất cả kiểm tra đã hoàn thành thành công!');

    } catch (error) {
        console.error('❌ Lỗi khi kiểm tra:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Chạy test nếu file này được gọi trực tiếp
if (require.main === module) {
    testCronJob().then(() => {
        console.log('\nKết thúc kiểm tra.');
        process.exit(0);
    }).catch(error => {
        console.error('Lỗi:', error);
        process.exit(1);
    });
}

module.exports = { testCronJob };
