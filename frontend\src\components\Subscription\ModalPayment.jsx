import React, { useState, useEffect, useCallback } from "react";
import { Modal, Typography, Spin, message, Button, Space, Divider } from "antd";
import {
  QrcodeOutlined,
  CopyOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import paymentService from "../../services/paymentService";
import "./ModalPayment.css";

const { Title, Text, Paragraph } = Typography;

const ModalPayment = ({
  visible,
  onClose,
  selectedPlan,
  billingCycle,
  workspaceId,
}) => {
  const [loading, setLoading] = useState(false);
  const [paymentData, setPaymentData] = useState(null);
  const [copied, setCopied] = useState(false);

  // Tính toán thông tin thanh toán
  const getPaymentInfo = () => {
    if (!selectedPlan) return null;

    const priceUSD =
      billingCycle === "yearly"
        ? selectedPlan.yearlyPrice
        : selectedPlan.monthlyPrice;
    const duration = billingCycle === "yearly" ? 12 : 1;
    const period = billingCycle === "yearly" ? "năm" : "tháng";

    return {
      packageId: selectedPlan.name.toLowerCase(),
      packageName: selectedPlan.name,
      amount: priceUSD,
      priceUSD: priceUSD,
      duration,
      period,
      description: selectedPlan.description,
    };
  };

  const createPayment = useCallback(async () => {
    try {
      setLoading(true);
      const paymentInfo = getPaymentInfo();

      const response = await paymentService.createPayment(
        workspaceId,
        paymentInfo.packageId,
        paymentInfo.duration,
        paymentInfo.amount
      );

      if (response.status === "success") {
        setPaymentData(response.data);
      } else {
        message.error("Không thể tạo mã thanh toán");
      }
    } catch (error) {
      console.error("Error creating payment:", error);
      message.error("Có lỗi xảy ra khi tạo thanh toán");
    } finally {
      setLoading(false);
    }
  }, [selectedPlan, workspaceId, billingCycle]);

  // Tạo thanh toán khi modal mở
  useEffect(() => {
    if (visible && selectedPlan && workspaceId) {
      createPayment();
    }
  }, [visible, selectedPlan, workspaceId, billingCycle, createPayment]);

  const handleClose = () => {
    setPaymentData(null);
    setCopied(false);
    onClose();
  };

  const paymentInfo = getPaymentInfo();

  return (
    <Modal
      title={
        <div style={{ textAlign: "center" }}>
          <QrcodeOutlined style={{ marginRight: 8, color: "#1890ff" }} />
          Thanh toán gói {paymentInfo?.packageName}
        </div>
      }
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={500}
      centered
      className="payment-modal"
    >
      {loading ? (
        <div style={{ textAlign: "center", padding: "40px 0" }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>Đang tạo mã thanh toán...</Text>
          </div>
        </div>
      ) : paymentData && paymentInfo ? (
        <div className="payment-content">
          {/* Thông tin gói */}
          <div className="package-info">
            <Title level={4} style={{ marginBottom: 8 }}>
              {paymentInfo.packageName}
            </Title>
            <Text type="secondary">{paymentInfo.description}</Text>
            <div style={{ marginTop: 8 }}>
              <Text strong style={{ fontSize: 18, color: "#1890ff" }}>
                {paymentInfo.amount.toLocaleString("vi-VN")} VND
              </Text>
              <Text type="secondary"> / {paymentInfo.period}</Text>
            </div>
          </div>

          <Divider />

          {/* Mã QR */}
          {paymentData?.data?.qrDataURL && (
            <div className="qr-section">
              <Title
                level={5}
                style={{
                  textAlign: "center",
                  marginBottom: 16,
                }}
              >
                Quét mã QR để thanh toán
              </Title>
              <div
                style={{
                  textAlign: "center",
                  marginBottom: 16,
                }}
              >
                <img
                  src={paymentData.data.qrDataURL}
                  alt="QR Code"
                  style={{
                    maxWidth: "200px",
                    border: "1px solid #d9d9d9",
                    borderRadius: "8px",
                  }}
                />
              </div>
            </div>
          )}

          {/* Lưu ý */}
          <div
            style={{
              marginTop: 16,
              padding: 12,
              backgroundColor: "#f6ffed",
              borderRadius: 6,
            }}
          >
            <Text type="secondary" style={{ fontSize: 12 }}>
              <CheckCircleOutlined
                style={{ color: "#52c41a", marginRight: 4 }}
              />
              Gói dịch vụ sẽ được kích hoạt sau khi thanh toán thành công.
            </Text>
          </div>
        </div>
      ) : (
        <div style={{ textAlign: "center", padding: "40px 0" }}>
          <Text type="secondary">Không thể tạo mã thanh toán</Text>
        </div>
      )}
    </Modal>
  );
};

export default ModalPayment;
