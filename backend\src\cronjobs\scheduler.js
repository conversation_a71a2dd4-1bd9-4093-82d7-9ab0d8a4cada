const cron = require('node-cron');
const { spawn } = require('child_process');
const path = require('path');

// C<PERSON>u hình các cron job
const CRON_JOBS = {
    // Chạy mỗi giờ để kiểm tra hóa đơn hết hạn
    autoUpdatePackage: {
        schedule: '0 * * * *', // Mỗi giờ vào phút 0
        script: path.join(__dirname, 'autoUpdatePackage.js'),
        description: 'Tự động cập nhật package hết hạn'
    },
    
    // Cron job hiện có cho Google sync
    autoAsync: {
        schedule: '*/30 * * * *', // Mỗi 30 phút
        script: path.join(__dirname, 'autoAsync.js'),
        description: 'Tự động đồng bộ với Google Task'
    }
};

function runScript(scriptPath, jobName) {
    console.log(`[${new Date().toISOString()}] Bắt đầu chạy ${jobName}`);
    
    const child = spawn('node', [scriptPath], {
        stdio: 'inherit',
        cwd: path.dirname(scriptPath)
    });

    child.on('close', (code) => {
        if (code === 0) {
            console.log(`[${new Date().toISOString()}] ${jobName} hoàn thành thành công`);
        } else {
            console.error(`[${new Date().toISOString()}] ${jobName} kết thúc với mã lỗi ${code}`);
        }
    });

    child.on('error', (error) => {
        console.error(`[${new Date().toISOString()}] Lỗi khi chạy ${jobName}:`, error.message);
    });
}

function startScheduler() {
    console.log('Khởi động Cron Job Scheduler...');
    
    Object.entries(CRON_JOBS).forEach(([jobName, config]) => {
        console.log(`Đăng ký cron job: ${jobName} - ${config.description}`);
        console.log(`Schedule: ${config.schedule}`);
        
        cron.schedule(config.schedule, () => {
            runScript(config.script, jobName);
        }, {
            scheduled: true,
            timezone: "Asia/Ho_Chi_Minh"
        });
    });
    
    console.log('Tất cả cron job đã được đăng ký thành công!');
}

// Chạy scheduler nếu file này được gọi trực tiếp
if (require.main === module) {
    startScheduler();
    
    // Giữ process chạy
    process.on('SIGINT', () => {
        console.log('\nDừng Cron Job Scheduler...');
        process.exit(0);
    });
    
    console.log('Cron Job Scheduler đang chạy. Nhấn Ctrl+C để dừng.');
}

module.exports = { startScheduler, CRON_JOBS };
