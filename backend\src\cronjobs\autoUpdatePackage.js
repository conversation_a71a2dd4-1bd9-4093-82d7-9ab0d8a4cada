const path = require("path");
const fs = require("fs");
const invoiceService = require("../services/invoiceService");

const LOCK_FILE = path.join("/tmp", "autoUpdatePackage.lock");
const LOG_FILE = path.join("/tmp", "autoUpdatePackage.log");
const MAX_EXECUTION_TIME = 5 * 60 * 1000; // 5 phút

function log(message, isError = false) {
  const timestamp = new Date().toISOString();
  const pid = process.pid;
  const logMessage = `[${timestamp}][PID:${pid}] ${message}\n`;

  const output = isError ? console.error : console.log;
  output(logMessage);

  try {
    fs.appendFileSync(LOG_FILE, logMessage);
  } catch (err) {
    console.error(
      `[${timestamp}][PID:${pid}] Ghi log thất bại: ${err.message}`
    );
  }
}

function isLocked() {
  return fs.existsSync(LOCK_FILE);
}

function lock() {
  fs.writeFileSync(LOCK_FILE, String(process.pid));
}

function unlock() {
  if (fs.existsSync(LOCK_FILE)) {
    fs.unlinkSync(LOCK_FILE);
  }
}

(async () => {
  const startTime = Date.now();

  if (isLocked()) {
    log("Đang có tiến trình khác đang chạy. Bỏ qua lần thực thi này.", true);
    return;
  }

  try {
    lock();
    log("Bắt đầu kiểm tra và cập nhật các hóa đơn hết hạn");

    const result = await invoiceService.processExpiredInvoices();

    if (result && typeof result === "object") {
      log(
        `Hoàn thành xử lý hóa đơn hết hạn: ${result.successfulUpdates}/${result.totalProcessed} thành công`
      );

      if (result.errors.length > 0) {
        log(`Có ${result.errors.length} lỗi xảy ra:`, true);
        result.errors.forEach((error) => {
          log(
            `- Hóa đơn ${error.invoiceId}, Workspace ${error.workspaceId}: ${error.error}`,
            true
          );
        });
      }

      if (result.totalProcessed === 0) {
        log("Không có hóa đơn nào cần xử lý");
      }
    } else {
      log("Kết quả không hợp lệ hoặc không có kết quả trả về.", true);
    }
  } catch (error) {
    log(`Lỗi khi xử lý hóa đơn hết hạn: ${error.message}`, true);
    log(`Stack trace: ${error.stack}`, true);
  } finally {
    unlock();
    const duration = Date.now() - startTime;
    log(`Thời gian thực thi: ${duration / 1000}s`);

    if (duration > MAX_EXECUTION_TIME) {
      log(
        `Cảnh báo: Job chạy quá thời gian giới hạn: ${duration / 1000}s`,
        true
      );
    }
  }
})();
