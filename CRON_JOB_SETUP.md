# Hướng dẫn Cron Job Tự động Cập nhật Package Hết hạn

## Tổng quan

Hệ thống cron job đã được tạo để tự động xử lý các hóa đơn hết hạn và cập nhật package workspace về gói Free khi hết hạn đăng ký.

## Các file đã tạo

### 1. Backend Services
- `backend/src/services/invoiceService.js` - Thêm method `processExpiredInvoices()`
- `backend/src/cronjobs/autoUpdatePackage.js` - Cron job chính
- `backend/src/cronjobs/scheduler.js` - Quản lý tất cả cron job
- `backend/src/cronjobs/index.js` - Module khởi tạo cron job
- `backend/src/cronjobs/test-cron.js` - Script test và kiểm tra
- `backend/src/cronjobs/README.md` - <PERSON><PERSON><PERSON> liệu chi tiết

### 2. C<PERSON><PERSON> hình
- `backend/package.json` - <PERSON>hê<PERSON> các script cron job
- `backend/server.js` - <PERSON><PERSON><PERSON> hợp cron job vào main server

## Chức năng chính

### autoUpdatePackage.js
- **Tần suất**: Chạy mỗi giờ (0 * * * *)
- **Chức năng**:
  - Tìm tất cả hóa đơn có status "paid" nhưng đã hết hạn
  - Cập nhật status từ "paid" sang "expired"
  - Cập nhật workspace về gói Free (packageId = 1)
  - Ghi log chi tiết quá trình xử lý
  - Báo cáo số lượng thành công/thất bại

### Cơ chế bảo vệ
- **Lock mechanism**: Tránh chạy đồng thời nhiều instance
- **Timeout protection**: Giới hạn thời gian chạy tối đa 5 phút
- **Error handling**: Xử lý lỗi từng hóa đơn riêng biệt
- **Detailed logging**: Ghi log vào `/tmp/autoUpdatePackage.log`

## Cách sử dụng

### 1. Chạy tự động (Khuyến nghị)
Cron job sẽ tự động chạy khi khởi động server:
```bash
npm start
# hoặc
npm run dev
```

### 2. Chạy thủ công
```bash
# Chạy tất cả cron job
npm run cron

# Chạy riêng cron job cập nhật package
npm run cron:update-package

# Kiểm tra trạng thái hệ thống
npm run cron:test
```

### 3. Chạy trong production
```bash
# Sử dụng PM2
pm2 start src/cronjobs/scheduler.js --name "cron-scheduler"

# Hoặc chạy background
nohup npm run cron > /tmp/cron.log 2>&1 &
```

## Kiểm tra và monitoring

### 1. Kiểm tra log
```bash
# Xem log realtime
tail -f /tmp/autoUpdatePackage.log

# Xem log lỗi
grep "ERROR\|Lỗi" /tmp/autoUpdatePackage.log
```

### 2. Kiểm tra trạng thái
```bash
# Chạy test script
npm run cron:test

# Kiểm tra process
ps aux | grep scheduler.js

# Kiểm tra lock file
ls -la /tmp/*.lock
```

### 3. Thống kê database
Script test sẽ hiển thị:
- Số hóa đơn hết hạn cần xử lý
- Số hóa đơn sắp hết hạn (7 ngày tới)
- Workspace có package trả phí
- Thống kê tổng quan hóa đơn

## Cấu hình tùy chỉnh

### Thay đổi tần suất chạy
Chỉnh sửa `backend/src/cronjobs/scheduler.js`:
```javascript
const CRON_JOBS = {
    autoUpdatePackage: {
        schedule: '0 * * * *', // Mỗi giờ
        // Thay đổi thành:
        // '0 */6 * * *' - Mỗi 6 giờ
        // '0 0 * * *' - Mỗi ngày lúc 0:00
        // '0 0 */7 * *' - Mỗi 7 ngày
    }
};
```

### Thay đổi package mặc định
Chỉnh sửa `backend/src/services/invoiceService.js`:
```javascript
// Thay đổi từ packageId = 1 sang package khác
await Workspace.update(
    { packageId: 2 }, // Thay đổi số này
    { where: { id: invoice.workspaceId } }
);
```

## Troubleshooting

### Cron job không chạy
1. Kiểm tra server có khởi động cron job không
2. Kiểm tra log file có lỗi không
3. Kiểm tra timezone setting

### Database connection error
1. Kiểm tra database connection trong main app
2. Kiểm tra environment variables
3. Restart cron scheduler

### Lock file bị treo
```bash
# Xóa lock file
rm /tmp/autoUpdatePackage.lock

# Restart scheduler
npm run cron
```

## Lưu ý quan trọng

1. **Backup database** trước khi chạy lần đầu
2. **Test trên môi trường development** trước
3. **Monitor log files** thường xuyên
4. **Kiểm tra disk space** cho log files
5. **Cấu hình log rotation** nếu cần thiết

## Liên hệ hỗ trợ

Nếu gặp vấn đề, kiểm tra:
1. Log files trong `/tmp/`
2. Database connection
3. Server memory và CPU usage
4. Cron job process status

Cron job đã được thiết kế để chạy ổn định và xử lý lỗi một cách graceful.
