const { Invoices, User, Workspace } = require("../models");
const { Op } = require("sequelize");

class InvoiceService {
  async createInvoice(invoiceData) {
    try {
      const invoice = await Invoices.create({
        userId: invoiceData.userId,
        workspaceId: invoiceData.workspaceId,
        amount: invoiceData.amount,
        status: "pending",
        packageId: invoiceData.packageId,
        packageDuration: invoiceData.packageDuration,
        packageStartDate: invoiceData.packageStartDate,
        packageEndDate: invoiceData.packageEndDate,
      });

      return invoice;
    } catch (error) {
      throw new Error(`Lỗi tạo hóa đơn: ${error.message}`);
    }
  }

  async createInvoiceTrial(invoiceData) {
    try {
      const invoice = await Invoices.create({
        userId: invoiceData.userId,
        workspaceId: invoiceData.workspaceId,
        amount: 0,
        status: "paid",
        packageId: invoiceData.packageId,
        packageDuration: null,
        //L<PERSON>y ngày hiện tại
        packageStartDate: new Date(),
        //L<PERSON>y ngày hiện tại + 3 ngày
        packageEndDate: new Date(new Date().setDate(new Date().getDate() + 3)),
      });

      //Cập nhật workspace
      const workspace = await Workspace.update(
        { packageId: invoiceData.packageId },
        { where: { id: invoiceData.workspaceId } }
      );

      console.log("Workspace updated:", workspace);

      // Cập nhật trạng thái dùng thử của user (đánh dấu đang ở trạng thái trial)
      const user = await User.update(
        { isTrial: true },
        { where: { id: invoiceData.userId } }
      );

      console.log("User updated (set isTrial=true):", user);

      return invoice;
    } catch (error) {
      throw new Error(`Lỗi tạo hóa đơn trial: ${error.message}`);
    }
  }

  async getInvoiceById(invoiceId) {
    try {
      const invoice = await Invoices.findByPk(invoiceId, {
        include: [
          { model: User, attributes: ["id", "name", "email"] },
          { model: Workspace, attributes: ["id", "name"] },
        ],
      });

      if (!invoice) {
        throw new Error("Không tìm thấy hóa đơn");
      }

      return invoice;
    } catch (error) {
      throw new Error(`Lỗi lấy thông tin hóa đơn: ${error.message}`);
    }
  }

  async getInvoicesByUser(userId, page = 1, limit = 10) {
    try {
      const offset = (page - 1) * limit;

      const { count, rows: invoices } = await Invoices.findAndCountAll({
        where: { userId },
        include: [{ model: Workspace, attributes: ["id", "name"] }],
        order: [["createdAt", "DESC"]],
        limit: parseInt(limit),
        offset: offset,
      });

      return {
        invoices,
        totalCount: count,
        totalPages: Math.ceil(count / limit),
        currentPage: parseInt(page),
      };
    } catch (error) {
      throw new Error(`Lỗi lấy danh sách hóa đơn: ${error.message}`);
    }
  }

  async getInvoicesByWorkspace(workspaceId, page = 1, limit = 10) {
    try {
      const offset = (page - 1) * limit;

      const { count, rows: invoices } = await Invoices.findAndCountAll({
        where: { workspaceId },
        include: [{ model: User, attributes: ["id", "name", "email"] }],
        order: [["createdAt", "DESC"]],
        limit: parseInt(limit),
        offset: offset,
      });

      return {
        invoices,
        totalCount: count,
        totalPages: Math.ceil(count / limit),
        currentPage: parseInt(page),
      };
    } catch (error) {
      throw new Error(`Lỗi lấy danh sách hóa đơn workspace: ${error.message}`);
    }
  }

  async getActivePackage(workspaceId) {
    try {
      const now = new Date();
      const activeInvoice = await Invoices.findOne({
        where: {
          workspaceId,
          status: "paid",
          packageStartDate: { [Op.lte]: now },
          packageEndDate: { [Op.gte]: now },
        },
        order: [["packageEndDate", "DESC"]],
      });

      return activeInvoice;
    } catch (error) {
      throw new Error(`Lỗi lấy gói đang hoạt động: ${error.message}`);
    }
  }

  async getInvoiceStats(userId, workspaceId = null) {
    try {
      const whereCondition = { userId };
      if (workspaceId) {
        whereCondition.workspaceId = workspaceId;
      }

      const [
        totalInvoices,
        paidInvoices,
        pendingInvoices,
        totalAmount,
        paidAmount,
      ] = await Promise.all([
        Invoices.count({ where: whereCondition }),
        Invoices.count({
          where: { ...whereCondition, status: "paid" },
        }),
        Invoices.count({
          where: { ...whereCondition, status: "pending" },
        }),
        Invoices.sum("amount", { where: whereCondition }),
        Invoices.sum("amount", {
          where: { ...whereCondition, status: "paid" },
        }),
      ]);

      return {
        totalInvoices,
        paidInvoices,
        pendingInvoices,
        totalAmount: totalAmount || 0,
        paidAmount: paidAmount || 0,
      };
    } catch (error) {
      throw new Error(`Lỗi lấy thống kê hóa đơn: ${error.message}`);
    }
  }

  async checkPackageExpiration() {
    try {
      const now = new Date();
      const expiringInvoices = await Invoices.findAll({
        where: {
          status: "paid",
          packageEndDate: {
            [Op.lt]: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000), // 7 ngày tới
          },
        },
        include: [
          { model: User, attributes: ["id", "email", "name"] },
          { model: Workspace, attributes: ["id", "name"] },
        ],
      });

      return expiringInvoices;
    } catch (error) {
      throw new Error(`Lỗi kiểm tra hết hạn gói: ${error.message}`);
    }
  }

  async processExpiredInvoices() {
    try {
      const now = new Date();

      // Tìm tất cả hóa đơn đã hết hạn nhưng vẫn có status "paid"
      const expiredInvoices = await Invoices.findAll({
        where: {
          status: "paid",
          packageEndDate: {
            [Op.lt]: now, // Đã hết hạn
          },
        },
        include: [
          { model: User, attributes: ["id", "email", "name"] },
          { model: Workspace, attributes: ["id", "name", "packageId"] },
        ],
      });

      const results = {
        totalProcessed: 0,
        successfulUpdates: 0,
        errors: [],
      };

      for (const invoice of expiredInvoices) {
        results.totalProcessed++;

        try {
          // Cập nhật trạng thái hóa đơn thành "expired"
          await Invoices.update(
            { status: "expired" },
            { where: { id: invoice.id } }
          );

          // Cập nhật workspace về gói Free (packageId = 1)
          await Workspace.update(
            { packageId: 1 },
            { where: { id: invoice.workspaceId } }
          );

          results.successfulUpdates++;

          console.log(
            `Đã cập nhật hóa đơn ID ${invoice.id} và workspace ID ${invoice.workspaceId} về gói Free`
          );
        } catch (error) {
          results.errors.push({
            invoiceId: invoice.id,
            workspaceId: invoice.workspaceId,
            error: error.message,
          });

          console.error(`Lỗi xử lý hóa đơn ID ${invoice.id}:`, error.message);
        }
      }

      return results;
    } catch (error) {
      throw new Error(`Lỗi xử lý hóa đơn hết hạn: ${error.message}`);
    }
  }
}

module.exports = new InvoiceService();
