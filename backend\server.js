const app = require("./src/app");
const { Server } = require("socket.io");
const { init, getIO } = require("./src/socket");
const { initializeCronJobs } = require("./src/cronjobs");
const http = require("http");
const jwt = require("jsonwebtoken");

const PORT = process.env.PORT || 3000;

const server = http.createServer(app);
const allowedOrigins =
  process.env.NODE_ENV === "production"
    ? ["https://gwtask.com", "https://www.gwtask.com"]
    : ["http://localhost:5173", "http://localhost:3000"];

const io = new Server(server, {
  cors: {
    origin: function (origin, callback) {
      // Allow requests with no origin (mobile apps, tools, etc.)
      if (!origin) return callback(null, true);

      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      } else {
        console.error(`Socket.IO CORS Error: Origin ${origin} not allowed`);
        return callback(new Error("Not allowed by CORS"));
      }
    },
    methods: ["GET", "POST", "OPTIONS"],
    credentials: true,
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
  },
  // Cấu hình cho CloudLinux Passenger - ưu tiên polling trước
  transports: ["polling", "websocket"],
  pingTimeout: 60000,
  pingInterval: 25000,
  // Cấu hình cho Passenger
  allowEIO3: true,
  connectTimeout: 45000,
  upgrade: true,
  // Cấu hình path cho Socket.IO
  path: "/socket.io/",
  // Cấu hình cho production với Passenger
  serveClient: false,
  // Thêm cấu hình để tương thích với Passenger
  allowRequest: (req, callback) => {
    // Cho phép tất cả requests trong production
    if (process.env.NODE_ENV === "production") {
      return callback(null, true);
    }
    // Kiểm tra origin trong development
    const origin = req.headers.origin;
    if (!origin || allowedOrigins.includes(origin)) {
      return callback(null, true);
    }
    return callback(null, false);
  },
});

init(io);

server.listen(PORT, () => {
  console.log(`Server is running at port ${PORT}`);
  console.log(
    `Socket.IO is configured with transports: ${io.engine.opts.transports.join(
      ", "
    )}`
  );
  console.log(`Environment: ${process.env.NODE_ENV || "development"}`);
  console.log(`Socket.IO path: ${io.engine.opts.path}`);
  if (process.env.NODE_ENV === "production") {
    console.log("Running on CloudLinux Passenger");
  }

  // Khởi động cron jobs
  try {
    initializeCronJobs();
  } catch (error) {
    console.error("Không thể khởi động cron jobs:", error.message);
  }
});

// app.listen(PORT, '0.0.0.0', () => {
//   console.log(`Server is running at port ${PORT}`);
// });
