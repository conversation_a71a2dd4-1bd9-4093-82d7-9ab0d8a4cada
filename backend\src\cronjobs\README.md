# Cron Jobs Documentation

## Tổng quan

Thư mục này chứa các cron job tự động để xử lý các tác vụ định kỳ trong hệ thống.

## C<PERSON><PERSON>ron Job hiện có

### 1. autoUpdatePackage.js
**M<PERSON><PERSON> đích**: Tự động cập nhật trạng thái hóa đơn hết hạn và package workspace

**Chức năng**:
- Tìm tất cả hóa đơn có status "paid" nhưng đã hết hạn (packageEndDate < hiện tại)
- Cập nhật trạng thái hóa đơn từ "paid" sang "expired"
- Cập nhật workspace về gói Free (packageId = 1)
- Ghi log chi tiết quá trình xử lý

**Lịch chạy**: Mỗi giờ vào phút 0 (0 * * * *)

### 2. autoAsync.js
**<PERSON><PERSON><PERSON> đích**: Tự động đồng bộ dữ liệu với Google Task

**Lịch chạy**: Mỗi 30 phút (*/30 * * * *)

## C<PERSON>ch sử dụng

### Chạy tất cả cron job
```bash
npm run cron
```

### Chạy từng cron job riêng lẻ
```bash
# Chạy cron job cập nhật package
npm run cron:update-package

# Chạy cron job đồng bộ Google Task
npm run cron:async
```

### Chạy trong môi trường production
```bash
# Sử dụng PM2 để quản lý process
pm2 start src/cronjobs/scheduler.js --name "cron-scheduler"

# Hoặc chạy trong background
nohup npm run cron > /tmp/cron.log 2>&1 &
```

## Cấu hình

### Thay đổi lịch chạy
Chỉnh sửa file `scheduler.js` để thay đổi lịch chạy:

```javascript
const CRON_JOBS = {
    autoUpdatePackage: {
        schedule: '0 * * * *', // Mỗi giờ
        // Thay đổi thành:
        // '0 */6 * * *' - Mỗi 6 giờ
        // '0 0 * * *' - Mỗi ngày lúc 0:00
        // '0 0 */7 * *' - Mỗi 7 ngày
    }
};
```

### Cron Schedule Format
```
* * * * *
│ │ │ │ │
│ │ │ │ └─── Ngày trong tuần (0-7, 0 và 7 = Chủ nhật)
│ │ │ └───── Tháng (1-12)
│ │ └─────── Ngày trong tháng (1-31)
│ └───────── Giờ (0-23)
└─────────── Phút (0-59)
```

## Log Files

Các log file được lưu trong `/tmp/`:
- `/tmp/autoUpdatePackage.log` - Log cho cron job cập nhật package
- `/tmp/autoAsync.log` - Log cho cron job đồng bộ Google Task

## Lock Mechanism

Mỗi cron job sử dụng lock file để tránh chạy đồng thời:
- `/tmp/autoUpdatePackage.lock`
- `/tmp/autoAsync.lock`

Nếu cron job bị treo, có thể xóa lock file thủ công:
```bash
rm /tmp/autoUpdatePackage.lock
rm /tmp/autoAsync.lock
```

## Monitoring

### Kiểm tra log
```bash
# Xem log realtime
tail -f /tmp/autoUpdatePackage.log

# Xem log lỗi
grep "ERROR\|Lỗi" /tmp/autoUpdatePackage.log
```

### Kiểm tra process
```bash
# Kiểm tra cron scheduler có đang chạy không
ps aux | grep scheduler.js

# Kiểm tra lock file
ls -la /tmp/*.lock
```

## Troubleshooting

### Cron job không chạy
1. Kiểm tra process scheduler có đang chạy không
2. Kiểm tra log file có lỗi không
3. Kiểm tra timezone setting

### Database connection error
1. Kiểm tra database connection trong main app
2. Kiểm tra environment variables
3. Restart cron scheduler

### Lock file bị treo
```bash
# Xóa tất cả lock file
rm /tmp/*.lock

# Restart scheduler
npm run cron
```
